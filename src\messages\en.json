{"form": {"auth": {"common": {"Full Name": "Full Name", "Email": "Email", "Password": "Password", "Confirm Password": "Confirm Password", "terms_policy_text": "By clicking continue, you agree to our <terms>Terms of Service</terms> and <policy>Privacy Policy</policy>."}, "login": {"form_title": "Welcome back", "form_subtitle": "Login to your Studib account", "external_auth": "Or login with", "submit": "<PERSON><PERSON>", "alternate_auth": "Don't have an account? <link>Sign Up</link>", "toast": {"success": "Login successful! Redirecting to Dashboard...", "error": "<PERSON><PERSON> failed. Please try again."}}, "signup": {"form_title": "Welcome to Studib", "form_subtitle": "Create your St<PERSON>b account", "external_auth": "Or sign up with", "submit": "Create Account", "alternate_auth": "Already have an account? <link>Log In</link>", "toast": {"success": "Account created successfully! Redirecting to Dashboard...", "error": "Account creation failed. Please try again."}}}}, "zod": {"validation": {"required": "This field is required", "invalid_format": "Invalid format", "invalid_type_received_string": "Expected string, received {received}", "invalid_type_received_number": "Expected number, received {received}", "too_small": "Must be at least {minimum} characters long", "too_big": "Maximum {maximum} characters long", "invalid_string_email": "Please enter a valid email address", "invalid_string_url": "Please enter a valid URL", "invalid_string_uuid": "Please enter a valid UUID", "invalid_string_regex": "Format is not valid", "invalid_string_format": "Invalid {validation} format", "invalid_value": "Invalid value", "custom": {"invalid_username_format": "Username can only contain alphanumeric characters, underscores, and dashes", "username_too_short": "Username must be at least 3 characters long", "full_name_too_short": "Full Name is required", "invalid_email_format": "Invalid email format", "password_too_short": "Password must be at least 6 characters long", "passwords_not_match": "Passwords and Confirm Password don't match", "description_too_short": "Description needs at least {minWords} meaningful words, found {currentWords}", "meaningful_content": "Description needs to have more meaningful content"}}}}