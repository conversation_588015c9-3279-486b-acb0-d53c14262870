"use client"

import AuthForm from "@/components/form/auth-form";
import { FcGoogle } from "react-icons/fc";
import { Card, CardContent } from "@/components/ui/card";
import Link from "next/link";
import { signUpFields } from "@/lib/schemas/auth";
import { useAuth } from "@/hooks/use-auth";
import { GlobalError } from "react-hook-form";
import { toast } from "sonner";
import { AuthError } from "@supabase/supabase-js";
import type { AuthFormOnSubmit } from "@/types/component";
import { signInWithGoogle } from "@/lib/supabase/actions";
import { useRouter } from "next/navigation";
import Image from "next/image";

const SignUpPage = () => {
  const { isLoading, signUp } = useAuth();
  const router = useRouter();

  const handleSubmit: AuthFormOnSubmit = async (formData, setError) => {
    try {
      await signUp(formData);
      toast.success("Account created successfully!");
      router.push("/");
    } catch (error: unknown) {
      const errorMessage =
        error instanceof AuthError
          ? error.message
          : error instanceof Error
            ? error.message
            : "An error occurred";

      setError("root", {
        type: "manual",
        message: `${errorMessage}!`,
      });
    }
  };

  const handleGoogleSignIn = async () => {
    try {
      await signInWithGoogle();
    } catch (error) {
      console.error('Error signing in with Google:', error);
    }
  };

  return (
    <div className="w-full h-svh max-h-svh">
      <div className="w-full h-full grid p-0 md:grid-cols-3">
        <div className="col-start-3 col-span-1">
          <div className="mt-4">
            <AuthForm
              className="grid"
              formTitle={(
                <div className="flex flex-col items-center text-center">
                  <Image
                    src="/img/logo.png"
                    alt="Darkness Store Logo"
                    width={48}
                    height={48}
                    priority
                    className="mb-4"
                  />
                  <h1 className="text-2xl font-bold">Welcome to Darkness Store</h1>
                  <p className="text-muted-foreground text-balance">
                    Create your new account
                  </p>
                </div>
              )}
              formError={({ message }: GlobalError) => {
                return (
                  <Card className="bg-destructive text-destructive-foreground text-sm py-4">
                    <CardContent className="px-4">
                      {message}
                    </CardContent>
                  </Card>
                )
              }}
              submitButtonContent={(
                <span>Sign Up</span>)
              }
              onSubmit={handleSubmit}
              isLoading={isLoading}
              fields={signUpFields}
              alternateAuth={(
                <div className="text-center text-sm">
                  Already have an account?{" "}
                  <Link href="/signup" className="underline underline-offset-4">
                    Sign Up
                  </Link>
                </div>
              )}
              externalAuth={{
                text: "Or continue with",
                options: [
                  {
                    icon: FcGoogle,
                    name: "Google",
                    onClick: handleGoogleSignIn,
                  }
                ],
              }}
            />
            <div className="text-muted-foreground *:[a]:hover:text-primary text-center text-xs text-balance *:[a]:underline *:[a]:underline-offset-4">
              By clicking continue, you agree to our <a href="#">Terms of Service</a>{" "}
              and <a href="#">Privacy Policy</a>.
            </div>
          </div>
        </div>
        <div className="hidden md:block row-start-1 col-start-1 col-span-2">
          <Image
            src="https://cdn.wallpapersafari.com/10/17/taTZD5.jpg"
            alt="Login Placeholder Image"
            className="h-full object-fill brightness-[0.5] dark:grayscale"
          />
        </div>
      </div>
    </div>
  );
};

export default SignUpPage;