import DashboardSidebar from "@/components/layout/dashboard-sidebar"
import DashboardHeader from "@/components/layout/dashboard-header"
import {
  SidebarProvider,
} from "@/components/ui/sidebar"

export default function ManagerLayout({
  children
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <SidebarProvider
      style={
        {
          "--sidebar-width": "calc(var(--spacing) * 72)",
          "--header-height": "calc(var(--spacing) * 12)",
        } as React.CSSProperties
      }
    >
      <DashboardSidebar variant="sidebar" />
      <div className="flex flex-1 flex-col">
        <DashboardHeader />
        <div className="flex flex-1 flex-col">
          {children}
        </div>
      </div>
    </SidebarProvider>
  )
}
