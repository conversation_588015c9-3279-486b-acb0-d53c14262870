"use client"

import { createContext, useContext, useEffect, useState } from "react";
import type { AuthContextType } from "@/types/hook";
import { createClient } from "@/lib/supabase/client";
import { User } from "@supabase/supabase-js";
import { signInWithGoogle } from "@/lib/supabase/actions";
// ---------------------------------

export const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const supabase = createClient()

  useEffect(() => {
    const getSession = async () => {
      const { data: { session } } = await supabase.auth.getSession()
      setUser(session?.user ?? null)
      setIsLoading(false)
    }

    getSession()

    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        setUser(session?.user ?? null)
        setIsLoading(false)
      }
    )

    return () => subscription.unsubscribe()
  }, [supabase])

  const login: AuthContextType['login'] = async ({
    email,
    password,
  }) => {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    })

    if (error) throw error
    return data
  }

  const signUp: AuthContextType['signUp'] = async (
    { full_name, email, password },
    emailRedirectTo,
  ) => {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        emailRedirectTo: emailRedirectTo,
        data: {
          full_name: full_name,
        }
      },
    })

    if (error) throw error
    return data
  }

  const signInWithOAuth = {
    google: signInWithGoogle,
  }

  const signOut = async () => {
    const { error } = await supabase.auth.signOut()
    if (error) throw error
  }

  const contextValue = {
    user,
    isLoading,
    login,
    signUp,
    signInWithOAuth,
    signOut,
  }

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}