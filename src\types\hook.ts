import { AuthResponse, AuthTokenResponsePassword, User } from "@supabase/supabase-js";
import { FieldValues } from "react-hook-form";

export interface AuthContextType {
  user: User | null
  isLoading: boolean
  login: (formData: FieldValues) => Promise<AuthTokenResponsePassword['data']>
  signUp: (formData: FieldValues, emailRedirectTo?: string) => Promise<AuthResponse['data']>
  signInWithOAuth: Record<string, () => Promise<void>>
  signOut: () => Promise<void>
}