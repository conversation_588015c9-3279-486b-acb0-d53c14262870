"use server"

import { Provider } from "@supabase/supabase-js";
import { createClient } from "@/lib/supabase/server";
import { redirect } from "next/navigation";

const signInWithOAuth = async (provider: Provider) => {
  const supabase = await createClient();
  const { data, error } = await supabase.auth.signInWithOAuth({
    provider,
    options: {
      redirectTo: `${process.env.NEXT_PUBLIC_SITE_URL}/api/auth/callback`,
    },
  });

  if (error) {
    throw error;
  }

  redirect(data.url);
};

const signInWithGoogle = async () => {
  await signInWithOAuth("google");
};

export { signInWithGoogle };