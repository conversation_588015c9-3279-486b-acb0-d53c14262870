import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { Column } from "@tanstack/react-table";
import { ArrowUp, ArrowUpDown } from "lucide-react";
import { ReactNode } from "react";

const SortableHeader = <TData, TValue>({ column, children }: { column: Column<TData, TValue>; children: ReactNode }) => {
  return (
    <Button
      variant="ghost"
      onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
      className="hover:bg-gray-100 font-semibold"
    >
      {children}
      {!column.getIsSorted() ? (
        <ArrowUpDown className="ml-2 h-4 w-4" />
      ) : (
        <ArrowUp className={cn(
          `text-primary ml-2 h-4 w-4 rotate-0 transition-transform duration-200`,
          column.getIsSorted() === 'desc' && 'rotate-180',
        )} />
      )}
    </Button>
  );
};
export default SortableHeader;