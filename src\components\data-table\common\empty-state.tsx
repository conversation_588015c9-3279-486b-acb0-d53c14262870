import { Search } from "lucide-react";

const EmptyState = ({ message }: { message: string }) => {
  return (
    <div className="flex flex-col items-center justify-center py-12 text-center">
      <div className="rounded-full bg-gray-100 p-3 mb-4">
        <Search className="h-6 w-6 text-gray-400" />
      </div>
      <h3 className="text-lg font-semibold text-gray-900 mb-1">No results found</h3>
      <p className="text-sm text-gray-500">{message}</p>
    </div>
  );
};
export default EmptyState;