import { z } from "zod";
import type { FieldConfig } from "@/types/component";

export const authSchema = {
  username: z.string().min(1, "Username is required").min(3, "Username must be at least 3 characters"),
  full_name: z.string().min(1, "Full Name is required"),
  email: z.email("Invalid email format"),
  password: z.string().min(1, "Password is required").min(6, "Password must be at least 6 characters"),
  confirm_password: z.string().min(1, "Confirm Password is required"),
};

export const loginFields: FieldConfig[] = [
  {
    name: "email",
    label: "Email",
    placeholder: "<EMAIL>",
    type: "email",
    schema: authSchema.email,
  },
  {
    name: "password",
    label: "Password",
    type: "password",
    schema: authSchema.password,
  },
];

export const signUpFields: FieldConfig[] = [
  {
    name: "full_name",
    label: "Full Name",
    placeholder: "Studib User",
    type: "text",
    schema: authSchema.full_name,
  },
  {
    name: "email",
    label: "Email",
    placeholder: "<EMAIL>",
    type: "email",
    schema: authSchema.email,
  },
  {
    name: "password",
    label: "Password",
    type: "password",
    schema: authSchema.password,
  },
  {
    name: "confirm_password",
    label: "Confirm Password",
    type: "password",
    schema: authSchema.confirm_password,
  },
];
