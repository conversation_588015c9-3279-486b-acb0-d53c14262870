import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";

const TableFilter = ({ table, placeholder }: { table: any; placeholder: string }) => {
  return (
    <div className="flex items-center py-4">
      <div className="relative flex-1 max-w-sm">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
        <Input
          placeholder={placeholder}
          value={table.getState().globalFilter ?? ''}
          onChange={(e) => table.setGlobalFilter(e.target.value)}
          className="pl-9"
        />
      </div>
    </div>
  );
};
export default TableFilter;