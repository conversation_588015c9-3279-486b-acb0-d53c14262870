import { Button } from "@/components/ui/button";
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { zodResolver } from "@hookform/resolvers/zod";
import { SubmitHand<PERSON>, useForm } from "react-hook-form";
import { z } from "zod";
import { Loader } from "@/components/ui/loader";
import { cn, toTitleCase } from "@/lib/utils";
import { useMemo } from "react";
import type { AuthFormProps } from "@/types/component";
// ------------------------------------

const AuthForm = ({
  className = "",
  formTitle,
  formError,
  submitButtonContent,
  onSubmit,
  isLoading,
  fields,
  alternateAuth,
  externalAuth,
  children,
  ...props
}: AuthFormProps) => {
  const { defaultValues, formSchema } = useMemo(() => {
    const defaultValues: Record<string, unknown> = {};
    const schemaShape: Record<string, z.ZodTypeAny> = {};

    fields.forEach((field) => {
      defaultValues[field.name] = field.value ?? "";
      schemaShape[field.name] = field.schema;
    });

    let schema = z.object(schemaShape);

    const confirmFields = fields.filter((field) => field.name.startsWith('confirm_'));

    // Confirm value fields
    schema = schema
      .refine((data) => {
        return confirmFields.every((field) => {
          const baseFieldName = field.name.replace('confirm_', '');
          return data[field.name] === data[baseFieldName];
        });
      })
      .superRefine((data, ctx) => {
        const invalidField = confirmFields.find((field) => {
          const baseFieldName = field.name.replace('confirm_', '');
          return data[field.name] !== data[baseFieldName];
        });


        if (invalidField) {
          let invalidFieldName = invalidField.name.replace('confirm_', '');
          invalidFieldName = toTitleCase(invalidFieldName);

          ctx.addIssue({
            code: 'custom',
            message: `${invalidFieldName} and Confirm ${invalidFieldName} does not match`,
            path: [invalidField.name],
          });
        }
      });

    return { defaultValues, formSchema: schema };
  }, [fields]);

  const form = useForm({
    resolver: zodResolver(formSchema),
    mode: "all",
    reValidateMode: "onChange",
    defaultValues: defaultValues
  });
  const { handleSubmit, setError, formState } = form;

  const wrappedOnSubmit: SubmitHandler<z.infer<typeof formSchema>> = (data, e) => {
    onSubmit(data, setError, e);
  };

  return (
    <Form {...form}>
      <form onSubmit={handleSubmit(wrappedOnSubmit)} className={cn("p-6 md:p-8", className)} {...props}>
        <div className="flex flex-col gap-6">
          <div>
            {formTitle}
          </div>

          {formError && formState.errors.root && (
            <>{formError(formState.errors.root)}</>
          )}

          {fields.map((field) => (
            <FormField
              key={field.name}
              name={field.name}
              render={({ field: formField }) => (
                <FormItem className="grid gap-3">
                  <FormLabel>{field.label}</FormLabel>
                  <FormControl>
                    <Input type={field.type} placeholder={field.placeholder} {...formField} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          ))}
          {children}

          <Button
            type="submit"
            disabled={isLoading}
            className="w-full cursor-pointer"
          >
            {isLoading ? <Loader size={24} /> : submitButtonContent}
          </Button>
          {externalAuth && (
            <>
              <div className="after:border-border relative text-center text-sm after:absolute after:inset-0 after:top-1/2 after:z-0 after:flex after:items-center after:border-t">
                <span className="bg-card text-muted-foreground relative z-10 px-2">{externalAuth.text}</span>
              </div>

              <div className="flex gap-4">
                {externalAuth.options.map((option, index) => (
                  <Button
                    key={index}
                    onClick={option.onClick}
                    variant="outline"
                    type="button"
                    className="w-full cursor-pointer"
                    data-oauth-provider={option.name.toLowerCase()}
                  >
                    {option.icon && <option.icon />}
                    {option.name}
                  </Button>
                ))}
              </div>
            </>
          )}
          {alternateAuth}
        </div>
      </form>
    </Form >
  );
};

export default AuthForm;