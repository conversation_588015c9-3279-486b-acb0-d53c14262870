export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instantiate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "12.2.3 (519615d)"
  }
  public: {
    Tables: {
      Attribute_Values: {
        Row: {
          attribute_id: number
          created_at: string | null
          updated_at: string | null
          value: string
          value_id: number
        }
        Insert: {
          attribute_id: number
          created_at?: string | null
          updated_at?: string | null
          value: string
          value_id?: number
        }
        Update: {
          attribute_id?: number
          created_at?: string | null
          updated_at?: string | null
          value?: string
          value_id?: number
        }
        Relationships: [
          {
            foreignKeyName: "fk_attribute_values_attribute_id"
            columns: ["attribute_id"]
            isOneToOne: false
            referencedRelation: "Attributes"
            referencedColumns: ["attribute_id"]
          },
        ]
      }
      Attributes: {
        Row: {
          attribute_id: number
          category_id: number
          created_at: string | null
          name: string
          type: Database["public"]["Enums"]["type"]
          updated_at: string | null
        }
        Insert: {
          attribute_id?: number
          category_id: number
          created_at?: string | null
          name: string
          type: Database["public"]["Enums"]["type"]
          updated_at?: string | null
        }
        Update: {
          attribute_id?: number
          category_id?: number
          created_at?: string | null
          name?: string
          type?: Database["public"]["Enums"]["type"]
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "fk_attributes_category_id"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "Categories"
            referencedColumns: ["category_id"]
          },
        ]
      }
      Brands: {
        Row: {
          brand_id: number
          created_at: string | null
          description: string | null
          is_active: boolean | null
          logo_url: string | null
          name: string
          updated_at: string | null
        }
        Insert: {
          brand_id?: number
          created_at?: string | null
          description?: string | null
          is_active?: boolean | null
          logo_url?: string | null
          name: string
          updated_at?: string | null
        }
        Update: {
          brand_id?: number
          created_at?: string | null
          description?: string | null
          is_active?: boolean | null
          logo_url?: string | null
          name?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      Cart_Items: {
        Row: {
          added_at: string | null
          cart_id: number
          product_id: number
          quantity: number
        }
        Insert: {
          added_at?: string | null
          cart_id: number
          product_id: number
          quantity: number
        }
        Update: {
          added_at?: string | null
          cart_id?: number
          product_id?: number
          quantity?: number
        }
        Relationships: [
          {
            foreignKeyName: "Cart_Items_cart_id_fkey"
            columns: ["cart_id"]
            isOneToOne: false
            referencedRelation: "Carts"
            referencedColumns: ["cart_id"]
          },
          {
            foreignKeyName: "Cart_Items_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "Products"
            referencedColumns: ["product_id"]
          },
        ]
      }
      Carts: {
        Row: {
          cart_id: number
          created_at: string | null
          updated_at: string | null
          user_id: number | null
        }
        Insert: {
          cart_id?: number
          created_at?: string | null
          updated_at?: string | null
          user_id?: number | null
        }
        Update: {
          cart_id?: number
          created_at?: string | null
          updated_at?: string | null
          user_id?: number | null
        }
        Relationships: []
      }
      Categories: {
        Row: {
          category_id: number
          created_at: string | null
          description: string | null
          name: string
          top_level: boolean
          updated_at: string | null
          url_slug: string
        }
        Insert: {
          category_id?: number
          created_at?: string | null
          description?: string | null
          name: string
          top_level?: boolean
          updated_at?: string | null
          url_slug: string
        }
        Update: {
          category_id?: number
          created_at?: string | null
          description?: string | null
          name?: string
          top_level?: boolean
          updated_at?: string | null
          url_slug?: string
        }
        Relationships: []
      }
      Category_Closure: {
        Row: {
          ancestor_id: number
          depth: number
          descendant_id: number
        }
        Insert: {
          ancestor_id: number
          depth: number
          descendant_id: number
        }
        Update: {
          ancestor_id?: number
          depth?: number
          descendant_id?: number
        }
        Relationships: [
          {
            foreignKeyName: "Category_Closure_ancestor_id_fkey"
            columns: ["ancestor_id"]
            isOneToOne: false
            referencedRelation: "Categories"
            referencedColumns: ["category_id"]
          },
          {
            foreignKeyName: "Category_Closure_descendant_id_fkey"
            columns: ["descendant_id"]
            isOneToOne: false
            referencedRelation: "Categories"
            referencedColumns: ["category_id"]
          },
        ]
      }
      Order_Items: {
        Row: {
          discount_amount: number | null
          order_id: number
          product_id: number
          quantity: number
          subtotal: number | null
          unit_price: number
        }
        Insert: {
          discount_amount?: number | null
          order_id: number
          product_id: number
          quantity: number
          subtotal?: number | null
          unit_price: number
        }
        Update: {
          discount_amount?: number | null
          order_id?: number
          product_id?: number
          quantity?: number
          subtotal?: number | null
          unit_price?: number
        }
        Relationships: [
          {
            foreignKeyName: "Order_Items_order_id_fkey"
            columns: ["order_id"]
            isOneToOne: false
            referencedRelation: "Orders"
            referencedColumns: ["order_id"]
          },
          {
            foreignKeyName: "Order_Items_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "Products"
            referencedColumns: ["product_id"]
          },
        ]
      }
      Orders: {
        Row: {
          canceled_at: string | null
          delivered_at: string | null
          order_date: string | null
          order_id: number
          payment_method: Database["public"]["Enums"]["payment_method"]
          savings: number | null
          shipped_at: string | null
          shipping_fee: number | null
          status: Database["public"]["Enums"]["order_status"]
          subtotal: number
          tax_amount: number | null
          total_amount: number
          tracking_number: string
          user_id: number
        }
        Insert: {
          canceled_at?: string | null
          delivered_at?: string | null
          order_date?: string | null
          order_id?: number
          payment_method?: Database["public"]["Enums"]["payment_method"]
          savings?: number | null
          shipped_at?: string | null
          shipping_fee?: number | null
          status: Database["public"]["Enums"]["order_status"]
          subtotal: number
          tax_amount?: number | null
          total_amount: number
          tracking_number: string
          user_id: number
        }
        Update: {
          canceled_at?: string | null
          delivered_at?: string | null
          order_date?: string | null
          order_id?: number
          payment_method?: Database["public"]["Enums"]["payment_method"]
          savings?: number | null
          shipped_at?: string | null
          shipping_fee?: number | null
          status?: Database["public"]["Enums"]["order_status"]
          subtotal?: number
          tax_amount?: number | null
          total_amount?: number
          tracking_number?: string
          user_id?: number
        }
        Relationships: []
      }
      Product_Attribute_Values: {
        Row: {
          created_at: string | null
          product_id: number
          updated_at: string | null
          value_id: number
        }
        Insert: {
          created_at?: string | null
          product_id: number
          updated_at?: string | null
          value_id: number
        }
        Update: {
          created_at?: string | null
          product_id?: number
          updated_at?: string | null
          value_id?: number
        }
        Relationships: [
          {
            foreignKeyName: "fk_product_attribute_values_product_id"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "Products"
            referencedColumns: ["product_id"]
          },
          {
            foreignKeyName: "fk_product_attribute_values_value_id"
            columns: ["value_id"]
            isOneToOne: false
            referencedRelation: "Attribute_Values"
            referencedColumns: ["value_id"]
          },
        ]
      }
      Product_Medias: {
        Row: {
          media_id: number
          media_type: Database["public"]["Enums"]["media_type"]
          media_url: string
          product_id: number
        }
        Insert: {
          media_id?: number
          media_type: Database["public"]["Enums"]["media_type"]
          media_url: string
          product_id: number
        }
        Update: {
          media_id?: number
          media_type?: Database["public"]["Enums"]["media_type"]
          media_url?: string
          product_id?: number
        }
        Relationships: [
          {
            foreignKeyName: "Product_Medias_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "Products"
            referencedColumns: ["product_id"]
          },
        ]
      }
      Products: {
        Row: {
          average_rating: number | null
          brand_id: number
          created_at: string | null
          description: string | null
          is_active: boolean | null
          low_stock_threshold: number
          name: string
          new_price: number
          original_price: number
          product_id: number
          sales_count: number | null
          sales_off: number | null
          specifications: Json | null
          stock_quantity: number
          thumbnail_url: string | null
          updated_at: string | null
          url_slug: string | null
          views_count: number | null
        }
        Insert: {
          average_rating?: number | null
          brand_id: number
          created_at?: string | null
          description?: string | null
          is_active?: boolean | null
          low_stock_threshold: number
          name: string
          new_price: number
          original_price: number
          product_id?: number
          sales_count?: number | null
          sales_off?: number | null
          specifications?: Json | null
          stock_quantity: number
          thumbnail_url?: string | null
          updated_at?: string | null
          url_slug?: string | null
          views_count?: number | null
        }
        Update: {
          average_rating?: number | null
          brand_id?: number
          created_at?: string | null
          description?: string | null
          is_active?: boolean | null
          low_stock_threshold?: number
          name?: string
          new_price?: number
          original_price?: number
          product_id?: number
          sales_count?: number | null
          sales_off?: number | null
          specifications?: Json | null
          stock_quantity?: number
          thumbnail_url?: string | null
          updated_at?: string | null
          url_slug?: string | null
          views_count?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "Products_brand_id_fkey"
            columns: ["brand_id"]
            isOneToOne: false
            referencedRelation: "Brands"
            referencedColumns: ["brand_id"]
          },
        ]
      }
      Products_Categories: {
        Row: {
          category_id: number
          created_at: string | null
          product_id: number
        }
        Insert: {
          category_id: number
          created_at?: string | null
          product_id: number
        }
        Update: {
          category_id?: number
          created_at?: string | null
          product_id?: number
        }
        Relationships: [
          {
            foreignKeyName: "Products_Categories_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "Categories"
            referencedColumns: ["category_id"]
          },
          {
            foreignKeyName: "Products_Categories_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "Products"
            referencedColumns: ["product_id"]
          },
        ]
      }
      Promotion_Applicability: {
        Row: {
          applicability_id: number
          category_id: number | null
          product_id: number | null
          promotion_id: number
          target_type: Database["public"]["Enums"]["target_type"]
        }
        Insert: {
          applicability_id?: number
          category_id?: number | null
          product_id?: number | null
          promotion_id: number
          target_type: Database["public"]["Enums"]["target_type"]
        }
        Update: {
          applicability_id?: number
          category_id?: number | null
          product_id?: number | null
          promotion_id?: number
          target_type?: Database["public"]["Enums"]["target_type"]
        }
        Relationships: [
          {
            foreignKeyName: "Promotion_Applicability_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "Categories"
            referencedColumns: ["category_id"]
          },
          {
            foreignKeyName: "Promotion_Applicability_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "Products"
            referencedColumns: ["product_id"]
          },
          {
            foreignKeyName: "Promotion_Applicability_promotion_id_fkey"
            columns: ["promotion_id"]
            isOneToOne: false
            referencedRelation: "Promotions"
            referencedColumns: ["promotion_id"]
          },
        ]
      }
      Promotions: {
        Row: {
          created_at: string | null
          description: string | null
          discount_type: Database["public"]["Enums"]["discount_type"]
          discount_value: number
          end_date: string
          is_active: boolean | null
          max_purchase_amount: number | null
          min_purchase_amount: number | null
          promo_code: string | null
          promotion_id: number
          start_date: string
          updated_at: string | null
          usage_limit: number
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          discount_type: Database["public"]["Enums"]["discount_type"]
          discount_value: number
          end_date?: string
          is_active?: boolean | null
          max_purchase_amount?: number | null
          min_purchase_amount?: number | null
          promo_code?: string | null
          promotion_id?: number
          start_date?: string
          updated_at?: string | null
          usage_limit?: number
        }
        Update: {
          created_at?: string | null
          description?: string | null
          discount_type?: Database["public"]["Enums"]["discount_type"]
          discount_value?: number
          end_date?: string
          is_active?: boolean | null
          max_purchase_amount?: number | null
          min_purchase_amount?: number | null
          promo_code?: string | null
          promotion_id?: number
          start_date?: string
          updated_at?: string | null
          usage_limit?: number
        }
        Relationships: []
      }
      Review_Medias: {
        Row: {
          file_size: number
          media_id: number
          media_type: Database["public"]["Enums"]["media_type"]
          media_url: string
          review_id: number
        }
        Insert: {
          file_size: number
          media_id?: number
          media_type: Database["public"]["Enums"]["media_type"]
          media_url: string
          review_id: number
        }
        Update: {
          file_size?: number
          media_id?: number
          media_type?: Database["public"]["Enums"]["media_type"]
          media_url?: string
          review_id?: number
        }
        Relationships: [
          {
            foreignKeyName: "Review_Medias_review_id_fkey"
            columns: ["review_id"]
            isOneToOne: false
            referencedRelation: "Reviews"
            referencedColumns: ["review_id"]
          },
        ]
      }
      Reviews: {
        Row: {
          cons: string | null
          created_at: string | null
          description: string
          is_shown: boolean | null
          product_id: number | null
          pros: string | null
          rating: number
          review_id: number
          title: string
          updated_at: string | null
          user_id: number | null
        }
        Insert: {
          cons?: string | null
          created_at?: string | null
          description: string
          is_shown?: boolean | null
          product_id?: number | null
          pros?: string | null
          rating: number
          review_id?: number
          title: string
          updated_at?: string | null
          user_id?: number | null
        }
        Update: {
          cons?: string | null
          created_at?: string | null
          description?: string
          is_shown?: boolean | null
          product_id?: number | null
          pros?: string | null
          rating?: number
          review_id?: number
          title?: string
          updated_at?: string | null
          user_id?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "Reviews_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "Products"
            referencedColumns: ["product_id"]
          },
        ]
      }
      Roles: {
        Row: {
          role_id: number
          role_name: string
        }
        Insert: {
          role_id?: number
          role_name: string
        }
        Update: {
          role_id?: number
          role_name?: string
        }
        Relationships: []
      }
      Wishlists: {
        Row: {
          added_at: string | null
          product_id: number
          user_id: number
        }
        Insert: {
          added_at?: string | null
          product_id: number
          user_id: number
        }
        Update: {
          added_at?: string | null
          product_id?: number
          user_id?: number
        }
        Relationships: [
          {
            foreignKeyName: "Wishlists_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "Products"
            referencedColumns: ["product_id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      get_category_tree: {
        Args: Record<PropertyKey, never>
        Returns: {
          name: string
          subcategories: Json[]
          url_slug: string
        }[]
      }
    }
    Enums: {
      discount_type: "percentage" | "fixed"
      media_type: "image" | "video"
      order_status:
        | "pending"
        | "shipped"
        | "delivered"
        | "canceled"
        | "returned"
      payment_method: "credit" | "monthly"
      target_type: "product" | "category" | "shipping_fee" | "all_target"
      type: "string" | "number" | "range"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      discount_type: ["percentage", "fixed"],
      media_type: ["image", "video"],
      order_status: ["pending", "shipped", "delivered", "canceled", "returned"],
      payment_method: ["credit", "monthly"],
      target_type: ["product", "category", "shipping_fee", "all_target"],
      type: ["string", "number", "range"],
    },
  },
} as const
