"use client"

import AuthForm from "@/components/form/auth-form";
import { Card, CardContent } from "@/components/ui/card";
import { FcGoogle } from "react-icons/fc";
import { signInWithGoogle } from "@/lib/supabase/actions";
import { useAuth } from "@/hooks/use-auth";
import { loginFields } from "@/lib/schemas/auth";
import { GlobalError } from "react-hook-form";
import Link from "next/link";
import { AuthError } from "@supabase/supabase-js";
import type { AuthFormOnSubmit } from "@/types/component";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import Image from "next/image";
// ------------------------------------

export default function LoginPage() {
  const { isLoading, login } = useAuth();
  const router = useRouter();

  const handleSubmit: AuthFormOnSubmit = async (formData, setError) => {
    try {
      await login(formData);
      toast.success("Login successful! Redirecting to dashboard...");
      router.push("/");
    } catch (error: unknown) {
      const errorMessage =
        error instanceof AuthError
          ? error.message
          : error instanceof Error
            ? error.message
            : "An error occurred";
      setError("root", {
        type: "manual",
        message: `${errorMessage}!`,
      });
    }
  };

  const handleGoogleSignIn = async () => {
    try {
      await signInWithGoogle();
    } catch (error) {
      console.error('Error signing in with Google:', error);
    }
  };

  return (
    <div className="grid min-h-svh lg:grid-cols-2">
      <div className="flex flex-col gap-4 p-6 md:p-10">
        <div className="flex justify-center gap-2 md:justify-start">
          <a href="#" className="flex items-center gap-2 font-medium">
            <div className="flex size-6 items-center justify-center rounded-md">
              <Image
                src="/img/logo.png"
                alt="Darkness Store Logo"
                width={64}
                height={64}
                priority
              />
            </div>
            Darkness Store
          </a>
        </div>
        <div className="flex flex-1 items-center justify-center">
          <div className="w-full max-w-md">
            <AuthForm
              className="grid"
              formTitle={(
                <div className="flex flex-col items-center text-center">
                  <h1 className="text-2xl font-bold">Welcome back</h1>
                  <p className="text-muted-foreground text-balance">
                    Login to your Darkness Store account
                  </p>
                </div>
              )}
              formError={({ message }: GlobalError) => {
                return (
                  <Card className="bg-destructive text-destructive-foreground text-sm py-4">
                    <CardContent className="px-4">
                      {message}
                    </CardContent>
                  </Card>
                )
              }}
              submitButtonContent={(
                <span>Login</span>)
              }
              onSubmit={handleSubmit}
              isLoading={isLoading}
              fields={loginFields}
              alternateAuth={(
                <div className="text-center text-sm">
                  Don&apos;t have an account?{" "}
                  <Link href="/signup" className="underline underline-offset-4">
                    Sign Up
                  </Link>
                </div>
              )}
              externalAuth={{
                text: "Or login with",
                options: [
                  {
                    icon: FcGoogle,
                    name: "Google",
                    onClick: handleGoogleSignIn,
                  }
                ],
              }}
            />
            <div className="text-muted-foreground *:[a]:hover:text-primary text-center text-xs text-balance *:[a]:underline *:[a]:underline-offset-4 px-4">
              By clicking continue, you agree to our <a href="#">Terms of Service</a>{" "}
              and <a href="#">Privacy Policy</a>.
            </div>
          </div>
        </div>
      </div>
      <div className="bg-muted relative hidden lg:block">
        <Image
          src="https://cdn.wallpapersafari.com/10/17/taTZD5.jpg"
          alt="Login Placeholder Image"
          className="h-full object-fill brightness-[0.5] dark:grayscale"
        />
      </div>
    </div>
  );
}
