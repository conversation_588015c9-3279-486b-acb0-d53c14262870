import { Montser<PERSON> } from "next/font/google";
import { Toaster } from "sonner";
import { Providers } from "@/app/providers";
import "@/styles/index.css";

const montserrat = Montserrat({
  variable: "--font-montserrat",
  subsets: ["latin"],
  display: "swap",
});

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${montserrat.className} ${montserrat.variable} antialiased`}
      >
        <div id="root">
          <Providers>
            {children}
          </Providers>
        </div>
        <Toaster
          position="top-center"
          richColors
          expand={true}
          closeButton
          toastOptions={{
            classNames: {
              content: 'grow',
              closeButton: '!static !inset-0 !transform-none !order-last',
            }
          }}
        />
      </body>
    </html>
  );
}
