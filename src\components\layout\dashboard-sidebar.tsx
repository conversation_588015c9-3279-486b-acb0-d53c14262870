"use client"

import * as React from "react"
import NavMain from "@/components/layout/nav/nav-main"
import { NavSecondary } from "@/components/layout/nav/nav-secondary"
import NavUser from "@/components/layout/nav/nav-user"
import {
  Sidebar,
  SidebarContent,
  Sidebar<PERSON>ooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"
import Image from "next/image";

const DashboardSidebar = ({ ...props }: React.ComponentProps<typeof Sidebar>) => {
  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              className="data-[slot=sidebar-menu-button]:!p-1.5"
            >
              <a href="#">
                <Image
                  src="/img/logo.png"
                  alt="Darkness Store Logo"
                  width={16}
                  height={16}
                  priority
                />
                <span className="text-base font-semibold">Darkness Store</span>
              </a>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <NavMain />
        <NavSecondary className="mt-auto" />
      </SidebarContent>
      <SidebarFooter>
        <NavUser />
      </SidebarFooter>
    </Sidebar>
  )
}
export default DashboardSidebar;