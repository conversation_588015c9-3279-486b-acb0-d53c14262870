import { HelpCircle, LucideIcon } from "lucide-react";
import {
  LayoutDashboard,
  Bell,
  ShoppingCart,
  Receipt,
  RotateCcw,
  Package,
  Warehouse,
  Star,
  Users,
  MessageSquare,
  MessageCircle,
  Megaphone,
  Percent,
  Gift,
  BarChart3,
  TrendingUp,
  Truck,
  FileText,
  ClipboardList,
  FileEdit,
  Image,
  Menu,
  Search,
  Globe,
  ShoppingBag,
  Store,
  Settings,
  Plus,
  ExternalLink,
} from "lucide-react";

export interface Badge {
  count?: number;
  variant?: "primary" | "warning" | "error" | "info" | "success";
  dot?: boolean;
  pulse?: boolean;
}

export interface NavItem {
  icon?: LucideIcon;
  label: string;
  path: string;
  badge?: Badge;
  subItems?: NavItem[];
}

export interface NavGroup {
  title: string | null;
  items: NavItem[];
}

export const SIDEBAR_ITEMS: NavGroup[] = [
  {
    title: null,
    items: [
      {
        icon: LayoutDashboard,
        label: "Dashboard",
        path: "/dashboard",
        badge: undefined,
      },
      {
        icon: Bell,
        label: "Notifications",
        path: "/notifications",
        badge: { count: 12, variant: "primary" },
      },
    ],
  },
  {
    title: "Sales",
    items: [
      {
        icon: ShoppingCart,
        label: "Orders",
        path: "/orders",
        badge: { count: 8, variant: "warning" },
      },
      {
        icon: Receipt,
        label: "Invoices",
        path: "/invoices",
      },
      {
        icon: RotateCcw,
        label: "Refunds",
        path: "/refunds",
        badge: { count: 2, variant: "error" },
      },
    ],
  },
  {
    title: "Catalog",
    items: [
      {
        icon: Package,
        label: "Products",
        path: "/products",
        subItems: [
          { label: "All Products", path: "/products/all" },
          { label: "Categories", path: "/products/categories" },
          { label: "Attributes", path: "/products/attributes" },
        ],
      },
      {
        icon: Warehouse,
        label: "Inventory",
        path: "/inventory",
        badge: { count: 5, variant: "error" },
        subItems: [
          { label: "Stock Levels", path: "/inventory/stock" },
          { label: "Low Stock", path: "/inventory/low-stock", badge: { count: 5 } },
          { label: "Out of Stock", path: "/inventory/out-of-stock" },
          { label: "Stock Adjustments", path: "/inventory/adjustments" },
          { label: "Warehouses", path: "/inventory/warehouses" },
          { label: "Suppliers", path: "/inventory/suppliers" },
        ],
      },
      {
        icon: Star,
        label: "Reviews",
        path: "/reviews",
        badge: { count: 15, variant: "info" },
      },
    ],
  },
  {
    title: "Customers",
    items: [
      {
        icon: Users,
        label: "Customers",
        path: "/customers",
        subItems: [
          { label: "All Customers", path: "/customers/all" },
          { label: "Segments", path: "/customers/segments" },
          { label: "VIP Customers", path: "/customers/vip" },
          { label: "At Risk", path: "/customers/at-risk" },
        ],
      },
      {
        icon: MessageSquare,
        label: "Support Tickets",
        path: "/support",
        badge: { count: 7, variant: "warning" },
      },
      {
        icon: MessageCircle,
        label: "Live Chat",
        path: "/live-chat",
        badge: { count: 2, variant: "success", pulse: true },
      },
    ],
  },
  {
    title: "Marketing",
    items: [
      {
        icon: Megaphone,
        label: "Campaigns",
        path: "/marketing/campaigns",
        subItems: [
          { label: "Email Campaigns", path: "/marketing/email" },
          { label: "SMS Campaigns", path: "/marketing/sms" },
          { label: "Push Notifications", path: "/marketing/push" },
        ],
      },
      {
        icon: Percent,
        label: "Discounts",
        path: "/discounts",
        subItems: [
          { label: "Coupon Codes", path: "/discounts/coupons" },
          { label: "Automatic Discounts", path: "/discounts/automatic" },
          { label: "Flash Sales", path: "/discounts/flash-sales" },
          { label: "Bulk Pricing", path: "/discounts/bulk" },
        ],
      },
      {
        icon: Gift,
        label: "Loyalty Program",
        path: "/loyalty",
      }
    ],
  },
  {
    title: "Analytics",
    items: [
      {
        icon: BarChart3,
        label: "Reports",
        path: "/reports",
        subItems: [
          { label: "Sales Reports", path: "/reports/sales" },
          { label: "Product Performance", path: "/reports/products" },
          { label: "Customer Analytics", path: "/reports/customers" },
          { label: "Marketing ROI", path: "/reports/marketing" },
          { label: "Financial Reports", path: "/reports/financial" },
          { label: "Custom Reports", path: "/reports/custom", icon: Plus },
        ],
      },
      {
        icon: TrendingUp,
        label: "Live Analytics",
        path: "/analytics/live",
      }
    ],
  },
  {
    title: "Operations",
    items: [
      {
        icon: Truck,
        label: "Shipping",
        path: "/shipping",
      },
      {
        icon: FileText,
        label: "Purchase Orders",
        path: "/purchase-orders",
      },
      {
        icon: ClipboardList,
        label: "Fulfillment",
        path: "/fulfillment",
        subItems: [
          { label: "Pick Lists", path: "/fulfillment/pick-lists" },
          { label: "Packing", path: "/fulfillment/packing" },
          { label: "Ready to Ship", path: "/fulfillment/ready" },
        ],
      },
    ],
  },
  {
    title: "Content",
    items: [
      {
        icon: FileEdit,
        label: "Pages",
        path: "/content/pages",
        subItems: [
          { label: "All Pages", path: "/content/pages/all" },
          { label: "Landing Pages", path: "/content/pages/landing" },
          { label: "Blog Posts", path: "/content/blog" },
        ],
      },
      {
        icon: Image,
        label: "Media Library",
        path: "/media",
      },
      {
        icon: Menu,
        label: "Navigation",
        path: "/navigation",
      },
      {
        icon: Search,
        label: "SEO",
        path: "/seo",
      },
    ],
  },
  {
    title: "Channels",
    items: [
      {
        icon: Globe,
        label: "Online Store",
        path: "/channels/online-store",
        badge: { dot: true, variant: "success" },
      },
      {
        icon: ShoppingBag,
        label: "Marketplaces",
        path: "/channels/marketplaces",
        subItems: [
          { label: "Amazon", path: "/channels/amazon", icon: ExternalLink },
          { label: "eBay", path: "/channels/ebay", icon: ExternalLink },
          { label: "Walmart", path: "/channels/walmart", icon: ExternalLink },
        ],
      },
      {
        icon: Store,
        label: "Point of Sale",
        path: "/channels/pos",
      },
    ],
  },
];

export const SECONDARY_NAV_ITEMS: NavGroup[] = [
  {
    title: null,
    items: [
      {
        icon: Settings,
        label: "Settings",
        path: "/settings/",
      },
      {
        icon: HelpCircle,
        label: "Get Help",
        path: "/help",
      },
      {
        icon: Search,
        label: "Search",
        path: "/search",
      },
    ],
  },
];

export const DASHBOARD_BASE_PATH = "/manager";

export const getFullPath = (path: string) => `${DASHBOARD_BASE_PATH}${path}`;