import { useTranslations } from 'next-intl';
import { z } from "zod";

export const createZodI18nMap = (t: ReturnType<typeof useTranslations>): z.ZodErrorMap => {
  const errorMap: z.ZodErrorMap = (issue) => {
    let message: string | undefined;
    switch (issue.code) {
      case "custom":
        // Handle custom validation messages
        if (issue.params?.i18nKey) {
          message = t(`validation.custom.${issue.params.i18nKey}`, issue.params);
        } else if (issue.message) {
          message = issue.message;
        }
        break;
      case "invalid_type":
        if (issue.expected === "undefined") {
          message = t('validation.required');
        } else if (issue.expected === "string") {
          message = t('validation.invalid_type_received_string', {
            received: typeof issue.input
          });
        } else if (issue.expected === "number") {
          message = t('validation.invalid_type_received_number', {
            received: typeof issue.input
          });
        } else {
          message = t('validation.invalid_value');
        }
        break;
      case "too_small":
        message = t('validation.too_small', {
          minimum: issue.minimum as number
        });
        break;
      case "too_big":
        message = t('validation.too_big', {
          maximum: issue.maximum as number
        });
        break;
      case "invalid_format":
        if (issue.format === 'email') {
          message = t('validation.invalid_string_email');
        } else if (issue.format === 'url') {
          message = t('validation.invalid_string_url');
        } else if (issue.format === 'uuid') {
          message = t('validation.invalid_string_uuid');
        } else if (issue.format === 'datetime') {
          message = t('validation.invalid_string_datetime');
        } else {
          message = t('validation.invalid_format');
        }
        break;
      case "invalid_value":
        message = t('validation.invalid_value');
        break;
      default:
        message = issue.message as string;
    }

    return message || t('validation.invalid_format');
  };
  return errorMap;
}