"use client"

import AuthForm from "@/components/form/auth-form";
import { Card, CardContent } from "@/components/ui/card";
import { useAuth } from "@/hooks/use-auth";
import { loginFields } from "@/lib/schemas/auth";
import { GlobalError } from "react-hook-form";
import { AuthError } from "@supabase/supabase-js";
import type { AuthFormOnSubmit } from "@/types/component";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import Image from "next/image";
// ------------------------------------

export default function LoginPage() {
  const { isLoading, login } = useAuth();
  const router = useRouter();

  const handleSubmit: AuthFormOnSubmit = async (formData, setError) => {
    try {
      await login(formData);
      toast.success("Login successful! Redirecting to dashboard...");
      router.replace("/manager/");
    } catch (error: unknown) {
      const errorMessage =
        error instanceof AuthError
          ? error.message
          : error instanceof Error
            ? error.message
            : "An error occurred";
      setError("root", {
        type: "manual",
        message: `${errorMessage}!`,
      });
    }
  };

  return (
    <div className="bg-muted flex min-h-svh flex-col items-center justify-center p-6 md:p-10">
      <div className="w-full max-w-sm md:max-w-xl">
        <div className="flex flex-col gap-6">
          <Card className="overflow-hidden p-0">
            <CardContent className="grid p-0 md:grid-cols-1">
              <AuthForm
                className="grid"
                formTitle={(
                  <div className="flex flex-col items-center text-center">
                    <Image
                      src="/img/logo.png"
                      alt="Darkness Store Logo"
                      width={48}
                      height={48}
                      priority
                      className="mb-4"
                    />
                    <h1 className="text-2xl font-bold">Welcome back</h1>
                    <p className="text-muted-foreground text-balance">
                      Login to your Darkness Store account
                    </p>
                  </div>
                )}
                formError={({ message }: GlobalError) => {
                  return (
                    <Card className="bg-destructive text-destructive-foreground text-sm py-4">
                      <CardContent className="px-4">
                        {message}
                      </CardContent>
                    </Card>
                  )
                }}
                submitButtonContent={(
                  <span>Login</span>)
                }
                onSubmit={handleSubmit}
                isLoading={isLoading}
                fields={loginFields}
              />
            </CardContent>
          </Card>
          <div className="text-muted-foreground *:[a]:hover:text-primary text-center text-xs text-balance *:[a]:underline *:[a]:underline-offset-4 px-4">
            By clicking continue, you agree to our <a href="#">Terms of Service</a>{" "}
            and <a href="#">Privacy Policy</a>.
          </div>
        </div>
      </div>
    </div>
  );
}
