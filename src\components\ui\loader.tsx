import { cn } from "@/lib/utils";

interface LoaderProps {
  className?: string;
  size?: number;
}

const Loader = ({
  className = "",
  size = 32,
}: LoaderProps) => {
  return (
    <span
      data-slot="loader"
      className={cn(`inline-block size-[${size}px]`, className)}
      role="status"
      aria-label="Loading"
    >
      <svg
        viewBox="0 0 50 50"
        className={`block animate-spin motion-safe:animate-spin motion-reduce:animate-spin`}
        width={size}
        height={size}
      >
        <circle
          data-slot="loader-circle"
          className="opacity-20 stroke-inherit"
          cx="25"
          cy="25"
          r="20"
          fill="none"
          strokeWidth="6"
        />
        <path
          data-slot="loader-path"
          className="opacity-100 stroke-inherit"
          fill="none"
          d="M25 5a20 20 0 0 1 20 20"
          strokeWidth="6"
          strokeLinecap="round"
        />
      </svg>
    </span>
  );
};

export { Loader };