"use client";

import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
} from "@/components/ui/sidebar";
import { useMemo, useState } from "react";
import { SIDEBAR_ITEMS, NavItem, getFullPath } from "@/config/nav-items";
import { ChevronRight } from "lucide-react";
import { usePathname } from "next/navigation";
import Link from "next/link";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";

const NavMain = () => {
  const pathname = usePathname();
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());

  const toggleItem = (key: string) => {
    setExpandedItems((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(key)) {
        newSet.delete(key);
      } else {
        newSet.add(key);
      }
      return newSet;
    });
  };

  const isPathActive = (path: string) => {
    return pathname === path || pathname.startsWith(`${path}/`);
  };

  // Reusable component to render a single nav item (with or without sub-items)
  const renderNavItem = (item: NavItem, depth: number = 0, parentKey: string = "") => {
    const hasSubItems = item.subItems && item.subItems.length > 0;
    const itemKey = parentKey ? `${parentKey}/${item.label}` : item.label;
    const isExpanded = expandedItems.has(itemKey);
    const isActive = isPathActive(item.path);
    const Icon = item.icon;

    const buttonContent = (
      <div>
        <Link href={getFullPath(item.path)} className="flex items-center gap-2">
          {Icon && <Icon className="h-4 w-4" />}
          <span className="flex-1">{item.label}</span>
          {item.badge && (
            <span
              className={`badge ${item.badge.dot ? "badge-dot" : "badge-count"} badge-${item.badge.variant || "default"
                } ${item.badge.pulse ? "animate-pulse" : ""}`}
            >
              {item.badge.count && !item.badge.dot && (item.badge.count > 99 ? "99+" : item.badge.count)}
            </span>
          )}
        </Link>
        {hasSubItems && (
          <ChevronRight
            className={`ml-auto h-4 w-4 transition-transform duration-200 ${isExpanded ? "rotate-90" : ""}`}
          />
        )}
      </div>
    );

    if (hasSubItems) {
      return (
        <Collapsible
          key={itemKey}
          open={isExpanded}
          onOpenChange={() => toggleItem(itemKey)}
        >
          <SidebarMenuItem>
            <CollapsibleTrigger asChild>
              <SidebarMenuButton
                tooltip={item.label}
                isActive={isActive}
                className="group/menu-item"
                asChild
              >
                {buttonContent}
              </SidebarMenuButton>
            </CollapsibleTrigger>
            <CollapsibleContent>
              <SidebarMenuSub>
                {item.subItems!.map((subItem) =>
                  renderNavItem(subItem, depth + 1, itemKey)
                )}
              </SidebarMenuSub>
            </CollapsibleContent>
          </SidebarMenuItem>
        </Collapsible>
      );
    }

    return (
      <SidebarMenuItem key={itemKey}>
        <SidebarMenuButton
          asChild
          tooltip={item.label}
          isActive={isActive}
          className={depth > 0 ? "pl-6" : ""}
        >
          {buttonContent}
        </SidebarMenuButton>
      </SidebarMenuItem>
    );
  };

  const memorized = useMemo(() => {
    return <>
      {SIDEBAR_ITEMS.map((group, idx) => {
        const groupKey = group.title || `group-${idx}`;

        return (
          <SidebarGroup key={groupKey}>
            {group.title && (
              <SidebarGroupLabel className="flex items-center justify-between cursor-pointer">
                {group.title}
              </SidebarGroupLabel>
            )}
            <SidebarGroupContent>
              <SidebarMenu>
                {group.items.map((item) => renderNavItem(item, 0, groupKey))}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        )
      })}
    </>;
  }, [pathname, expandedItems, renderNavItem]);

  return memorized;
};

export default NavMain;